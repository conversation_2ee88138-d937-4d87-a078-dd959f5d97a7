# Production Environment Variables for ICU Dataset Application
# These variables override .env settings when NODE_ENV=production

# AWS Configuration (Frontend Direct Upload)
REACT_APP_AWS_IDENTITY_POOL_ID=ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
REACT_APP_AWS_REGION=ap-southeast-2
REACT_APP_S3_BUCKET=icudatasetphrasesfortesting

# Backend Configuration - Intentionally set to localhost to trigger static fallback
# This ensures the S3ProgressDisplay uses static data instead of trying to connect to backend
REACT_APP_BACKEND_URL=http://localhost:5000

# Application Configuration
REACT_APP_NAME=ICU Dataset Application
REACT_APP_VERSION=1.0.0
NODE_ENV=production
REACT_APP_DEBUG=false

# Note: When a proper backend is deployed, update REACT_APP_BACKEND_URL to the live backend URL
# Example: REACT_APP_BACKEND_URL=https://icu-dataset-backend.herokuapp.com
