/**
 * Test Script for Noongar ICU Words Categories
 * 
 * This script verifies that the new Noongar language categories have been
 * correctly added to the ICU dataset application and are functioning properly.
 * 
 * Usage: Run this in the browser console on the phrase selection page
 */

console.log('🧪 Starting Noongar Categories Verification...');

// Expected Noongar phrases
const expectedNoongarPart1 = [
    "mentditj (sick)",
    "kabi (water)",
    "koboorl wirt (hungry)",
    "koolboo (cough)",
    "moort (family)",
    "koort (partner)",
    "kaat (head)",
    "moolymari (face)",
    "moo-yl (throat)",
    "meow (eyes)",
    "daa-r (mouth)",
    "ngort (chest)",
    "marr (arm)",
    "maaraka (hand)",
    "maarak birika (fingers)"
];

const expectedNoongarPart2 = [
    "mart (leg)",
    "koort (heart)",
    "djena (feet)",
    "bookarl (sore)",
    "nyin (sit)",
    "karlang (hot)",
    "nyidiny (cold)"
];

// Function to verify Noongar categories are available
function verifyNoongarCategoriesExist() {
    console.log('\n📋 Checking for Noongar categories in phrase selection...');
    
    // Look for category buttons or elements
    const categoryElements = document.querySelectorAll('[data-testid*="category"], .category-button, .category-item, .MuiButton-root');
    
    let noongarPart1Found = false;
    let noongarPart2Found = false;
    
    categoryElements.forEach((element, index) => {
        const text = element.textContent || element.innerText || '';
        
        if (text.includes('Noongar ICU Words Part 1')) {
            noongarPart1Found = true;
            console.log(`✅ Found "Noongar ICU Words Part 1" category`);
        }
        
        if (text.includes('Noongar ICU Words Part 2')) {
            noongarPart2Found = true;
            console.log(`✅ Found "Noongar ICU Words Part 2" category`);
        }
    });
    
    if (!noongarPart1Found) {
        console.error('❌ "Noongar ICU Words Part 1" category not found');
    }
    
    if (!noongarPart2Found) {
        console.error('❌ "Noongar ICU Words Part 2" category not found');
    }
    
    return noongarPart1Found && noongarPart2Found;
}

// Function to verify phrase counts
function verifyPhraseCounts() {
    console.log('\n🔢 Verifying phrase counts...');
    
    // Check if we can access the phrases object
    if (typeof window.phrases !== 'undefined') {
        const phrases = window.phrases;
        
        if (phrases['Noongar ICU Words Part 1']) {
            const part1Count = phrases['Noongar ICU Words Part 1'].length;
            console.log(`✅ Noongar ICU Words Part 1: ${part1Count} phrases (expected: 15)`);
            
            if (part1Count !== 15) {
                console.error(`❌ Expected 15 phrases in Part 1, found ${part1Count}`);
                return false;
            }
        } else {
            console.error('❌ Noongar ICU Words Part 1 not found in phrases object');
            return false;
        }
        
        if (phrases['Noongar ICU Words Part 2']) {
            const part2Count = phrases['Noongar ICU Words Part 2'].length;
            console.log(`✅ Noongar ICU Words Part 2: ${part2Count} phrases (expected: 7)`);
            
            if (part2Count !== 7) {
                console.error(`❌ Expected 7 phrases in Part 2, found ${part2Count}`);
                return false;
            }
        } else {
            console.error('❌ Noongar ICU Words Part 2 not found in phrases object');
            return false;
        }
        
        return true;
    } else {
        console.warn('⚠️ Phrases object not accessible from window. This is normal if not on the phrase selection page.');
        return null;
    }
}

// Function to verify specific phrase content
function verifyPhraseContent() {
    console.log('\n📝 Verifying phrase content...');
    
    if (typeof window.phrases !== 'undefined') {
        const phrases = window.phrases;
        
        // Check Part 1 phrases
        if (phrases['Noongar ICU Words Part 1']) {
            const part1Phrases = phrases['Noongar ICU Words Part 1'];
            let part1Correct = true;
            
            expectedNoongarPart1.forEach((expectedPhrase, index) => {
                if (part1Phrases[index] === expectedPhrase) {
                    console.log(`✅ Part 1[${index}]: "${expectedPhrase}"`);
                } else {
                    console.error(`❌ Part 1[${index}]: Expected "${expectedPhrase}", found "${part1Phrases[index]}"`);
                    part1Correct = false;
                }
            });
            
            if (!part1Correct) return false;
        }
        
        // Check Part 2 phrases
        if (phrases['Noongar ICU Words Part 2']) {
            const part2Phrases = phrases['Noongar ICU Words Part 2'];
            let part2Correct = true;
            
            expectedNoongarPart2.forEach((expectedPhrase, index) => {
                if (part2Phrases[index] === expectedPhrase) {
                    console.log(`✅ Part 2[${index}]: "${expectedPhrase}"`);
                } else {
                    console.error(`❌ Part 2[${index}]: Expected "${expectedPhrase}", found "${part2Phrases[index]}"`);
                    part2Correct = false;
                }
            });
            
            if (!part2Correct) return false;
        }
        
        return true;
    } else {
        console.warn('⚠️ Cannot verify phrase content - phrases object not accessible');
        return null;
    }
}

// Function to test category selection
function testCategorySelection() {
    console.log('\n🖱️ Testing category selection...');
    
    // Try to find and click Noongar category buttons
    const categoryButtons = document.querySelectorAll('button, .category-button, .MuiButton-root');
    
    let noongarPart1Button = null;
    let noongarPart2Button = null;
    
    categoryButtons.forEach(button => {
        const text = button.textContent || button.innerText || '';
        
        if (text.includes('Noongar ICU Words Part 1')) {
            noongarPart1Button = button;
        }
        
        if (text.includes('Noongar ICU Words Part 2')) {
            noongarPart2Button = button;
        }
    });
    
    if (noongarPart1Button) {
        console.log('✅ Found Noongar ICU Words Part 1 button - testing click...');
        try {
            noongarPart1Button.click();
            console.log('✅ Successfully clicked Noongar ICU Words Part 1');
        } catch (error) {
            console.error('❌ Error clicking Noongar ICU Words Part 1:', error);
            return false;
        }
    } else {
        console.error('❌ Noongar ICU Words Part 1 button not found');
        return false;
    }
    
    if (noongarPart2Button) {
        console.log('✅ Found Noongar ICU Words Part 2 button - testing click...');
        try {
            noongarPart2Button.click();
            console.log('✅ Successfully clicked Noongar ICU Words Part 2');
        } catch (error) {
            console.error('❌ Error clicking Noongar ICU Words Part 2:', error);
            return false;
        }
    } else {
        console.error('❌ Noongar ICU Words Part 2 button not found');
        return false;
    }
    
    return true;
}

// Main verification function
function runNoongarVerification() {
    console.log('🚀 Running full Noongar categories verification...\n');
    
    const results = {
        categoriesExist: verifyNoongarCategoriesExist(),
        phraseCounts: verifyPhraseCounts(),
        phraseContent: verifyPhraseContent(),
        categorySelection: testCategorySelection()
    };
    
    console.log('\n📊 Verification Results:');
    console.log(`   Categories Exist: ${results.categoriesExist ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`   Phrase Counts: ${results.phraseCounts === true ? '✅ PASS' : results.phraseCounts === false ? '❌ FAIL' : '⚠️ PENDING'}`);
    console.log(`   Phrase Content: ${results.phraseContent === true ? '✅ PASS' : results.phraseContent === false ? '❌ FAIL' : '⚠️ PENDING'}`);
    console.log(`   Category Selection: ${results.categorySelection ? '✅ PASS' : '❌ FAIL'}`);
    
    const overallSuccess = results.categoriesExist && results.categorySelection && 
                          (results.phraseCounts !== false) && (results.phraseContent !== false);
    
    console.log(`\n🎯 Overall Status: ${overallSuccess ? '✅ SUCCESS' : '❌ NEEDS ATTENTION'}`);
    
    if (overallSuccess) {
        console.log('\n🎉 Noongar categories verification completed successfully!');
        console.log('   Both Noongar ICU Words categories are properly integrated');
        console.log('   and functioning correctly in the application.');
    } else {
        console.log('\n⚠️ Some verification checks failed. Please review the results above.');
    }
    
    return overallSuccess;
}

// Instructions for manual testing
console.log('\n📖 Manual Testing Instructions:');
console.log('1. Navigate to the ICU dataset application');
console.log('2. Complete consent and demographic forms');
console.log('3. Go to the phrase selection page');
console.log('4. Run: runNoongarVerification()');
console.log('5. Look for "Noongar ICU Words Part 1" and "Noongar ICU Words Part 2" categories');
console.log('6. Test selecting phrases from both Noongar categories');

// Export functions for manual use
window.verifyNoongarCategoriesExist = verifyNoongarCategoriesExist;
window.verifyPhraseCounts = verifyPhraseCounts;
window.verifyPhraseContent = verifyPhraseContent;
window.testCategorySelection = testCategorySelection;
window.runNoongarVerification = runNoongarVerification;

console.log('\n✨ Noongar verification script loaded! Use runNoongarVerification() to start testing.');
