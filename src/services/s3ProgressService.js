/**
 * S3 Progress Service
 * Real-time progress tracking system that queries AWS S3 bucket directly
 * to display accurate data collection statistics for the ICU dataset application
 */

import { fetchSampleCounts, getSampleProgress, hasMetSampleTarget } from './s3SampleCountService';
import { getAllPhrases, getCategoryNames, getPhrasesByCategory } from '../phrases';

// Configuration
const DEFAULT_TARGET_PER_PHRASE = 20; // Default target recordings per phrase
const CACHE_DURATION = 60 * 60 * 1000; // 1 hour cache duration (optimised for performance)
const CACHE_KEY = 'icuApp_s3ProgressCache';
const BACKUP_CACHE_KEY = 'icuApp_s3ProgressBackup';

class S3ProgressService {
  constructor() {
    this.cache = null;
    this.lastFetch = null;
    this.isLoading = false;
    this.targetPerPhrase = DEFAULT_TARGET_PER_PHRASE;
  }

  /**
   * Set the target number of recordings per phrase
   * @param {number} target - Target recordings per phrase
   */
  setTargetPerPhrase(target) {
    this.targetPerPhrase = target;
    console.log(`📊 Target recordings per phrase set to: ${target}`);
  }

  /**
   * Get the current target per phrase
   * @returns {number} Current target per phrase
   */
  getTargetPerPhrase() {
    return this.targetPerPhrase;
  }

  /**
   * Check if cached data is still valid
   * @returns {boolean} True if cache is valid
   */
  isCacheValid() {
    if (!this.cache || !this.lastFetch) return false;
    
    const now = Date.now();
    const cacheAge = now - this.lastFetch;
    return cacheAge < CACHE_DURATION;
  }

  /**
   * Load cached data from localStorage
   */
  loadCacheFromStorage() {
    try {
      const stored = localStorage.getItem(CACHE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        if (parsed.timestamp && (Date.now() - parsed.timestamp) < CACHE_DURATION) {
          this.cache = parsed.data;
          this.lastFetch = parsed.timestamp;
          console.log('📱 Loaded S3 progress data from localStorage cache');
          return true;
        }
      }
    } catch (error) {
      console.warn('⚠️ Failed to load S3 progress cache from localStorage:', error);
    }
    return false;
  }

  /**
   * Save data to localStorage cache
   * @param {Object} data - Data to cache
   */
  saveCacheToStorage(data) {
    try {
      const cacheData = {
        data,
        timestamp: Date.now()
      };
      localStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
      // Also save a backup copy
      localStorage.setItem(BACKUP_CACHE_KEY, JSON.stringify({
        timestamp: Date.now(),
        data: data
      }));
      console.log('💾 Saved S3 progress data to localStorage cache');
    } catch (error) {
      console.warn('⚠️ Failed to save S3 progress cache to localStorage:', error);
    }
  }

  /**
   * Normalize phrase key for S3 lookup
   * Handles variations in phrase naming between UI and S3 storage
   */
  normalizePhraseKey(phrase) {
    return phrase
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '') // Remove special characters except spaces
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .trim();
  }

  /**
   * Find phrase count in S3 data, trying multiple key variations
   */
  findPhraseCount(byPhraseData, originalPhrase, normalizedKey) {
    if (!byPhraseData) return 0;

    // Try exact match first
    if (byPhraseData[originalPhrase] !== undefined) {
      return byPhraseData[originalPhrase];
    }

    // Try normalized key
    if (byPhraseData[normalizedKey] !== undefined) {
      return byPhraseData[normalizedKey];
    }

    // Try common variations
    const variations = [
      originalPhrase.toLowerCase(),
      originalPhrase.replace(/\s+/g, '_'),
      originalPhrase.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '_'),
      normalizedKey.replace(/_/g, ' '),
      normalizedKey.replace(/_/g, '')
    ];

    for (const variation of variations) {
      if (byPhraseData[variation] !== undefined) {
        return byPhraseData[variation];
      }
    }

    return 0;
  }

  /**
   * Fetch real-time progress data from S3
   * @param {boolean} forceRefresh - Force refresh even if cache is valid
   * @returns {Promise<Object>} Progress data
   */
  async fetchProgressData(forceRefresh = false) {
    // Return cached data if valid and not forcing refresh
    if (!forceRefresh && this.isCacheValid()) {
      console.log('📊 Using cached S3 progress data');
      return this.cache;
    }

    // Try to load from localStorage if memory cache is invalid
    if (!forceRefresh && this.loadCacheFromStorage()) {
      return this.cache;
    }

    // Prevent multiple simultaneous fetches
    if (this.isLoading) {
      console.log('⏳ S3 progress fetch already in progress, waiting...');
      // Wait for current fetch to complete
      while (this.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.cache;
    }

    this.isLoading = true;
    console.log('🔄 Fetching real-time progress data from S3...');

    try {
      // Fetch sample counts from S3
      const sampleCounts = await fetchSampleCounts();

      if (!sampleCounts.success) {
        throw new Error(`Failed to fetch S3 data: ${sampleCounts.error}`);
      }

      // Debug: Show S3 phrase data
      console.log('📊 S3 sample counts received:', {
        total: sampleCounts.counts.total,
        phraseCount: Object.keys(sampleCounts.counts.byPhrase).length,
        topPhrases: Object.entries(sampleCounts.counts.byPhrase)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 5)
          .map(([phrase, count]) => `${phrase}: ${count}`)
      });

      // Get all categories and phrases
      const categories = getCategoryNames();
      const phrasesByCategory = {};
      categories.forEach(category => {
        phrasesByCategory[category] = getPhrasesByCategory(category);
      });

      // Calculate progress for each phrase across all categories
      const phraseProgress = {};
      const categoryProgress = {};
      let totalRecordings = 0;
      let totalTargetRecordings = 0;
      let completedPhrases = 0;

      // Process each category and its phrases
      categories.forEach(category => {
        const categoryPhrases = phrasesByCategory[category];
        let categoryRecordings = 0;
        let categoryCompleted = 0;
        let categoryTarget = categoryPhrases.length * this.targetPerPhrase;

        categoryPhrases.forEach(phrase => {
          // Create phrase key for S3 lookup - handle phrase name variations
          const phraseKey = this.normalizePhraseKey(phrase);
          const currentCount = this.findPhraseCount(sampleCounts.counts.byPhrase, phrase, phraseKey);
          const progress = Math.min(100, Math.round((currentCount / this.targetPerPhrase) * 100));
          const isCompleted = currentCount >= this.targetPerPhrase;

          // Debug logging for phrase matching
          if (currentCount > 0) {
            console.log(`✅ S3ProgressService: Found ${currentCount} recordings for phrase "${phrase}" (key: "${phraseKey}")`);
          } else {
            console.log(`⚠️ S3ProgressService: No recordings found for phrase "${phrase}" (key: "${phraseKey}")`);
          }

          phraseProgress[phrase] = {
            current: currentCount,
            target: this.targetPerPhrase,
            progress: progress,
            completed: isCompleted,
            remaining: Math.max(0, this.targetPerPhrase - currentCount),
            category: category,
            phraseKey: phraseKey
          };

          categoryRecordings += currentCount;
          totalRecordings += currentCount;
          totalTargetRecordings += this.targetPerPhrase;

          if (isCompleted) {
            completedPhrases++;
            categoryCompleted++;
          }
        });

        // Store category-level progress
        categoryProgress[category] = {
          current: categoryRecordings,
          target: categoryTarget,
          progress: categoryTarget > 0 ? Math.round((categoryRecordings / categoryTarget) * 100) : 0,
          completed: categoryCompleted,
          total: categoryPhrases.length,
          remaining: categoryPhrases.length - categoryCompleted
        };
      });

      // Calculate overall progress
      const overallProgress = totalTargetRecordings > 0 
        ? Math.round((totalRecordings / totalTargetRecordings) * 100) 
        : 0;

      // Calculate total phrases across all categories
      const totalPhrases = categories.reduce((sum, category) => sum + phrasesByCategory[category].length, 0);

      const progressData = {
        // Overall statistics
        overall: {
          progress: overallProgress,
          totalRecordings,
          totalTargetRecordings,
          completedPhrases,
          totalPhrases,
          phrasesRemaining: totalPhrases - completedPhrases
        },

        // Category-specific progress
        categories: categoryProgress,

        // Phrase-specific progress
        phrases: phraseProgress,

        // Raw S3 data for debugging
        rawData: sampleCounts,

        // Metadata
        lastUpdated: new Date().toISOString(),
        targetPerPhrase: this.targetPerPhrase,
        dataSource: sampleCounts.source || 'S3',
        cacheExpiry: Date.now() + CACHE_DURATION
      };

      // Cache the results
      this.cache = progressData;
      this.lastFetch = Date.now();
      this.saveCacheToStorage(progressData);

      console.log('✅ S3 progress data fetched successfully:', {
        overallProgress: `${overallProgress}%`,
        totalRecordings,
        completedPhrases: `${completedPhrases}/${allPhrases.length}`,
        dataSource: sampleCounts.source
      });

      return progressData;

    } catch (error) {
      console.error('❌ Error fetching S3 progress data:', error);
      
      // Return fallback data if available
      if (this.cache) {
        console.log('📊 Using stale cached data due to fetch error');
        return {
          ...this.cache,
          error: error.message,
          isStale: true
        };
      }

      // Return empty progress data as fallback
      return this.createFallbackProgressData(error.message);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Create fallback progress data when S3 fetch fails
   * @param {string} errorMessage - Error message
   * @returns {Object} Fallback progress data
   */
  createFallbackProgressData(errorMessage) {
    const allPhrases = getAllPhrases();
    const phraseProgress = {};

    allPhrases.forEach(phrase => {
      phraseProgress[phrase] = {
        current: 0,
        target: this.targetPerPhrase,
        progress: 0,
        completed: false,
        remaining: this.targetPerPhrase
      };
    });

    return {
      overall: {
        progress: 0,
        totalRecordings: 0,
        totalTargetRecordings: allPhrases.length * this.targetPerPhrase,
        completedPhrases: 0,
        totalPhrases: allPhrases.length,
        phrasesRemaining: allPhrases.length
      },
      phrases: phraseProgress,
      rawData: null,
      lastUpdated: new Date().toISOString(),
      targetPerPhrase: this.targetPerPhrase,
      dataSource: 'fallback',
      error: errorMessage,
      isFallback: true
    };
  }

  /**
   * Get progress data for a specific phrase
   * @param {string} phrase - Phrase to get progress for
   * @returns {Promise<Object>} Phrase progress data
   */
  async getPhraseProgress(phrase) {
    const progressData = await this.fetchProgressData();
    return progressData.phrases[phrase] || {
      current: 0,
      target: this.targetPerPhrase,
      progress: 0,
      completed: false,
      remaining: this.targetPerPhrase
    };
  }

  /**
   * Get overall project progress
   * @returns {Promise<Object>} Overall progress data
   */
  async getOverallProgress() {
    const progressData = await this.fetchProgressData();
    return progressData.overall;
  }

  /**
   * Get top performing phrases (highest completion rates)
   * @param {number} limit - Number of top phrases to return
   * @returns {Promise<Array>} Array of top phrases with progress data
   */
  async getTopPhrases(limit = 10) {
    const progressData = await this.fetchProgressData();

    return Object.entries(progressData.phrases)
      .map(([phrase, data]) => ({ phrase, ...data }))
      .sort((a, b) => b.progress - a.progress)
      .slice(0, limit);
  }

  /**
   * Get phrases that need more recordings (lowest completion rates)
   * @param {number} limit - Number of phrases to return
   * @returns {Promise<Array>} Array of phrases needing recordings
   */
  async getPhrasesNeedingRecordings(limit = 10) {
    const progressData = await this.fetchProgressData();

    return Object.entries(progressData.phrases)
      .map(([phrase, data]) => ({ phrase, ...data }))
      .filter(item => !item.completed)
      .sort((a, b) => a.progress - b.progress)
      .slice(0, limit);
  }

  /**
   * Clear all cached data
   */
  clearCache() {
    this.cache = null;
    this.lastFetch = null;
    try {
      localStorage.removeItem(CACHE_KEY);
      console.log('🗑️ S3 progress cache cleared');
    } catch (error) {
      console.warn('⚠️ Failed to clear S3 progress cache:', error);
    }
  }

  /**
   * Get cache status information
   * @returns {Object} Cache status
   */
  getCacheStatus() {
    return {
      hasCache: !!this.cache,
      isValid: this.isCacheValid(),
      lastFetch: this.lastFetch,
      cacheAge: this.lastFetch ? Date.now() - this.lastFetch : null,
      isLoading: this.isLoading
    };
  }

  /**
   * Force refresh progress data from S3
   * @returns {Promise<Object>} Fresh progress data
   */
  async refreshProgressData() {
    console.log('🔄 Force refreshing S3 progress data...');
    return await this.fetchProgressData(true);
  }

  /**
   * Get formatted progress summary for display
   * @returns {Promise<Object>} Formatted progress summary
   */
  async getProgressSummary() {
    const progressData = await this.fetchProgressData();

    return {
      overallPercentage: progressData.overall.progress,
      recordingsCollected: progressData.overall.totalRecordings,
      recordingsNeeded: progressData.overall.totalTargetRecordings,
      phrasesCompleted: progressData.overall.completedPhrases,
      totalPhrases: progressData.overall.totalPhrases,
      lastUpdated: progressData.lastUpdated,
      dataSource: progressData.dataSource,
      isStale: progressData.isStale || false,
      error: progressData.error || null
    };
  }
}

// Create and export singleton instance
const s3ProgressService = new S3ProgressService();
export default s3ProgressService;

// Export configuration constants
export { DEFAULT_TARGET_PER_PHRASE, CACHE_DURATION };
