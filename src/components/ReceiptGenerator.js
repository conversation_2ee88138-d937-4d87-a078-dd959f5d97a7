import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Button, 
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Divider
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import PrintIcon from '@mui/icons-material/Print';
import EmailIcon from '@mui/icons-material/Email';
import DownloadIcon from '@mui/icons-material/Download';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

/**
 * Component for generating and displaying a receipt for the user's recording session
 */
const ReceiptGenerator = ({ demographicInfo, sessionRecordingsCount, onClose }) => {
  const [copied, setCopied] = useState(false);
  const [emailDialogOpen, setEmailDialogOpen] = useState(false);
  const [email, setEmail] = useState('');
  
  // Generate a unique receipt ID based on demographic info and timestamp
  const generateReceiptId = () => {
    // Create a hash from demographic info and current timestamp
    const timestamp = new Date().getTime();
    const demoString = demographicInfo ? 
      `${demographicInfo.age}-${demographicInfo.gender}-${demographicInfo.languageBackground}` : 
      'anonymous';
    
    // Create a simple hash
    let hash = 0;
    const str = `${demoString}-${timestamp}`;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    
    // Format as a readable ID with prefix
    const prefix = 'ICU';
    const hashStr = Math.abs(hash).toString(16).toUpperCase().padStart(8, '0');
    return `${prefix}-${hashStr}`;
  };
  
  // Generate receipt ID once when component mounts
  const receiptId = React.useMemo(generateReceiptId, [demographicInfo]);
  
  // Format current date for receipt
  const formatDate = () => {
    const now = new Date();
    return now.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  // Handle copy to clipboard
  const handleCopy = () => {
    navigator.clipboard.writeText(receiptId);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };
  
  // Handle print receipt
  const handlePrint = () => {
    const receiptContent = document.getElementById('receipt-content');
    const printWindow = window.open('', '_blank');
    
    printWindow.document.write(`
      <html>
        <head>
          <title>ICU Dataset Recording Receipt</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            .receipt { border: 1px solid #ccc; padding: 20px; max-width: 500px; margin: 0 auto; }
            .header { text-align: center; margin-bottom: 20px; }
            .receipt-id { font-size: 24px; font-weight: bold; margin: 20px 0; text-align: center; }
            .divider { border-top: 1px dashed #ccc; margin: 15px 0; }
            .footer { text-align: center; font-size: 12px; margin-top: 30px; color: #666; }
          </style>
        </head>
        <body>
          <div class="receipt">
            <div class="header">
              <h1>ICU Dataset Recording Receipt</h1>
            </div>
            <p><strong>Date:</strong> ${formatDate()}</p>
            <p><strong>Recordings Contributed:</strong> ${sessionRecordingsCount}</p>
            <div class="divider"></div>
            <p class="receipt-id">Receipt ID: ${receiptId}</p>
            <div class="divider"></div>
            <p>Thank you for your contribution to the ICU Dataset project. If you wish to withdraw your consent in the future, please contact our team with this receipt ID.</p>
            <div class="footer">
              <p>ICU Dataset Project - ${new Date().getFullYear()}</p>
            </div>
          </div>
        </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
  };
  
  // Handle download as PDF
  const handleDownload = () => {
    // Create a data URL for a simple text file
    const receiptText = `
ICU DATASET RECORDING RECEIPT
=============================
Date: ${formatDate()}
Recordings Contributed: ${sessionRecordingsCount}
Receipt ID: ${receiptId}

Thank you for your contribution to the ICU Dataset project.
If you wish to withdraw your consent in the future, please contact our team with this receipt ID.

ICU Dataset Project - ${new Date().getFullYear()}
    `.trim();
    
    const blob = new Blob([receiptText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ICU_Receipt_${receiptId}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  
  // Handle email dialog
  const handleEmailOpen = () => {
    setEmailDialogOpen(true);
  };
  
  const handleEmailClose = () => {
    setEmailDialogOpen(false);
  };
  
  const handleEmailSend = () => {
    // In a real app, this would send an email via backend
    // For now, we'll just simulate it with a timeout
    setTimeout(() => {
      setEmailDialogOpen(false);
      setEmail('');
      alert('Receipt sent to ' + email);
    }, 1000);
  };
  
  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 3 }}>
      <Paper 
        elevation={3} 
        sx={{ 
          p: 4, 
          maxWidth: 500, 
          width: '100%',
          borderRadius: 2,
          border: '1px solid #e0e0e0'
        }}
        id="receipt-content"
      >
        <Typography variant="h4" align="center" gutterBottom sx={{ color: '#2196f3' }}>
          Recording Receipt
        </Typography>
        
        <Typography variant="body1" paragraph>
          Thank you for contributing to the ICU Dataset project. Please save this receipt in case you need to withdraw your consent in the future.
        </Typography>
        
        <Divider sx={{ my: 2 }} />
        
        <Box sx={{ my: 3 }}>
          <Typography variant="body2" color="text.secondary">
            Date
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
            {formatDate()}
          </Typography>
        </Box>
        
        <Box sx={{ my: 3 }}>
          <Typography variant="body2" color="text.secondary">
            Recordings Contributed
          </Typography>
          <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
            {sessionRecordingsCount}
          </Typography>
        </Box>
        
        <Box sx={{ my: 3 }}>
          <Typography variant="body2" color="text.secondary">
            Receipt ID
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
            <Typography variant="h6" sx={{ fontWeight: 'bold', flexGrow: 1 }}>
              {receiptId}
            </Typography>
            <IconButton 
              onClick={handleCopy} 
              color={copied ? "success" : "primary"} 
              size="small"
              sx={{ ml: 1 }}
            >
              {copied ? <CheckCircleIcon /> : <ContentCopyIcon />}
            </IconButton>
          </Box>
        </Box>
        
        <Divider sx={{ my: 2 }} />
        
        <Box sx={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap', gap: 1, mt: 3 }}>
          <Button 
            variant="outlined" 
            startIcon={<PrintIcon />}
            onClick={handlePrint}
          >
            Print
          </Button>
          
          <Button 
            variant="outlined" 
            startIcon={<DownloadIcon />}
            onClick={handleDownload}
          >
            Download
          </Button>
          
          <Button 
            variant="outlined" 
            startIcon={<EmailIcon />}
            onClick={handleEmailOpen}
          >
            Email
          </Button>
          
          <Button 
            variant="contained" 
            color="primary"
            onClick={onClose}
            sx={{ mt: { xs: 2, sm: 0 }, width: { xs: '100%', sm: 'auto' } }}
          >
            Done
          </Button>
        </Box>
        
        {/* Email Dialog */}
        <Dialog open={emailDialogOpen} onClose={handleEmailClose}>
          <DialogTitle>Email Receipt</DialogTitle>
          <DialogContent>
            <TextField
              autoFocus
              margin="dense"
              id="email"
              label="Email Address"
              type="email"
              fullWidth
              variant="outlined"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleEmailClose}>Cancel</Button>
            <Button onClick={handleEmailSend} variant="contained" color="primary">
              Send
            </Button>
          </DialogActions>
        </Dialog>
      </Paper>
    </Box>
  );
};

export default ReceiptGenerator;
