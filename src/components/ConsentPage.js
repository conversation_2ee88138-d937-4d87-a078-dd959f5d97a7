import React from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  Divider,
  Card,
  CardContent,
  Grid
} from '@mui/material';

const ConsentPage = ({ onConsent }) => {
  const [consentChecked, setConsentChecked] = React.useState(false);

  const handleCheckboxChange = (event) => {
    setConsentChecked(event.target.checked);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box className="consent-container">
        {/* Header Section with Two-Column Layout */}
        <Box
          className="consent-header"
          sx={{
            mb: 4,
            background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
            color: 'white',
            borderRadius: 3,
            p: 5,
            boxShadow: '0 8px 32px rgba(25, 118, 210, 0.3)',
            minHeight: '400px',
            position: 'relative'
          }}
        >
          <Grid container spacing={2} alignItems="center" sx={{ height: '100%' }}>
            {/* Left Column - Page Title (Centered) */}
            <Grid item xs={12} md={6}>
              <Box sx={{
                textAlign: 'center',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                px: { xs: 2, md: 3 }
              }}>
                <Typography variant="h2" component="h1" sx={{ fontWeight: 'bold', mb: 2, fontSize: { xs: '2.5rem', md: '3.5rem' } }}>
                  ICU Lipreader
                </Typography>
                <Typography variant="h4" component="h2" sx={{ opacity: 0.9, fontSize: { xs: '1.5rem', md: '2rem' } }}>
                  Volunteer Consent Form
                </Typography>
              </Box>
            </Grid>

            {/* Right Column - Patient and Nurse Image (Larger with Rounded Corners) */}
            <Grid item xs={12} md={6}>
              <Box sx={{
                display: 'flex',
                justifyContent: { xs: 'center', md: 'flex-end' },
                alignItems: 'center',
                width: '100%',
                overflow: 'hidden',
                pl: { xs: 0, md: 1 },
                pr: { xs: 0, md: 2 }
              }}>
                <Box
                  component="img"
                  src="/images/patient-nurse-consent.png"
                  alt="Patient and Nurse"
                  sx={{
                    width: 'auto',
                    maxWidth: { xs: '400px', md: '650px' },
                    height: 'auto',
                    maxHeight: { xs: '400px', md: '650px' },
                    minWidth: { xs: '350px', md: '500px' },
                    objectFit: 'contain',
                    borderRadius: '18px',
                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
                  }}
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'flex';
                  }}
                />
                <Box
                  sx={{
                    width: 'auto',
                    maxWidth: { xs: '400px', md: '650px' },
                    minWidth: { xs: '350px', md: '500px' },
                    height: { xs: '400px', md: '650px' },
                    display: 'none',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    borderRadius: '18px',
                    border: '2px dashed rgba(255, 255, 255, 0.5)'
                  }}
                >
                  <Typography variant="h6" sx={{ color: 'rgba(255, 255, 255, 0.8)', textAlign: 'center', fontWeight: 'bold' }}>
                    Patient &<br/>Nurse<br/>Image
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Grid container spacing={4}>
          {/* Main Content */}
          <Grid item xs={12} md={8}>
            <Card elevation={3} sx={{ mb: 3 }}>
              <CardContent sx={{ p: 4 }}>
                <Typography variant="h5" gutterBottom color="primary" sx={{ fontWeight: 'bold', mb: 3 }}>
                  📋 Project Purpose
                </Typography>
                <Typography paragraph sx={{ fontSize: '1.1rem', lineHeight: 1.7 }}>
                  Thank you for your interest in participating in our project. The purpose of this initiative is to collect video samples of ICU-related phrases to train a lip-reading model. This model will be used to create a mobile lip-reading app that supports patients in Intensive Care units who cannot use their voice to communicate. This project is supported by the South Metro Health Service Kaartdijin Innovation Centre.
                </Typography>
              </CardContent>
            </Card>

            <Card elevation={3} sx={{ mb: 3 }}>
              <CardContent sx={{ p: 4 }}>
                <Typography variant="h5" gutterBottom color="primary" sx={{ fontWeight: 'bold', mb: 3 }}>
                  🎯 What Your Participation Involves
                </Typography>
                <Typography paragraph sx={{ fontSize: '1.1rem', lineHeight: 1.7 }}>
                  You will be asked to record the bottom half of your face by silently mouthing words and phrases that are commonly used in Intensive Care settings. Each phrase will need to be recorded 3 times to provide sufficient training data for the model. The application will guide you through this process.
                </Typography>
              </CardContent>
            </Card>

            <Card elevation={3} sx={{ mb: 3 }}>
              <CardContent sx={{ p: 4 }}>
                <Typography variant="h5" gutterBottom color="primary" sx={{ fontWeight: 'bold', mb: 3 }}>
                  🔒 Data Usage and Privacy
                </Typography>
                <Typography paragraph sx={{ fontSize: '1.1rem', lineHeight: 1.7 }}>
                  The video recordings collected will be used solely for the purpose of training and improving the LipNet lip-reading model. Your recordings will be stored securely in AWS S3 cloud storage and will only be accessible to the project team. All video recordings are anonymised and non-identifiable, focusing on the mouth region only. Personal identifiers will not be published in any project outputs.
                </Typography>
              </CardContent>
            </Card>

            <Card elevation={3} sx={{ mb: 4 }}>
              <CardContent sx={{ p: 4 }}>
                <Typography variant="h5" gutterBottom color="primary" sx={{ fontWeight: 'bold', mb: 3 }}>
                  ✋ Voluntary Participation
                </Typography>
                <Typography paragraph sx={{ fontSize: '1.1rem', lineHeight: 1.7 }}>
                  Your participation in this project is entirely voluntary. You must be 18 years old or over to participate. You may withdraw your consent up until the 1st of September 2025 by contacting the project team and quoting the recording receipt number you receive upon completion, therefore allowing us to remove your recordings from the dataset.
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          {/* Sidebar */}
          <Grid item xs={12} md={4}>
            <Card 
              elevation={4} 
              sx={{ 
                position: 'sticky',
                top: 20,
                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                border: '2px solid #e3f2fd'
              }}
            >
              <CardContent sx={{ p: 4 }}>
                <Typography variant="h5" gutterBottom color="primary" sx={{ fontWeight: 'bold', mb: 3 }}>
                  ✅ Consent Agreement
                </Typography>
                
                <Box sx={{ 
                  mb: 4, 
                  p: 3, 
                  bgcolor: 'rgba(25, 118, 210, 0.08)', 
                  borderRadius: 2, 
                  border: '2px solid rgba(25, 118, 210, 0.2)' 
                }}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentChecked}
                        onChange={handleCheckboxChange}
                        color="primary"
                        size="large"
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: '1rem', lineHeight: 1.5 }}>
                        I have read and understood the information provided. I consent to my video recordings being used in training the lip-reading model for this application.
                      </Typography>
                    }
                  />
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
                  <Button
                    variant="contained"
                    color="primary"
                    size="large"
                    disabled={!consentChecked}
                    onClick={onConsent}
                    sx={{ 
                      minWidth: 250, 
                      py: 2, 
                      px: 4, 
                      fontSize: '1.2rem',
                      fontWeight: 'bold',
                      borderRadius: 3,
                      background: consentChecked 
                        ? 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)'
                        : undefined,
                      boxShadow: consentChecked 
                        ? '0 4px 20px rgba(25, 118, 210, 0.4)'
                        : undefined,
                      '&:hover': {
                        background: consentChecked 
                          ? 'linear-gradient(135deg, #1565c0 0%, #1976d2 100%)'
                          : undefined,
                        transform: consentChecked ? 'translateY(-2px)' : undefined,
                      },
                      transition: 'all 0.3s ease'
                    }}
                  >
                    🚀 I Consent - Let's Begin!
                  </Button>
                </Box>

                <Divider sx={{ my: 3 }} />
                
                <Box sx={{ 
                  p: 3, 
                  bgcolor: 'rgba(76, 175, 80, 0.08)', 
                  borderRadius: 2,
                  border: '1px solid rgba(76, 175, 80, 0.2)'
                }}>
                  <Typography variant="h6" gutterBottom color="success.main" sx={{ fontWeight: 'bold' }}>
                    📞 Contact Information
                  </Typography>
                  <Typography variant="body2" color="textSecondary" sx={{ lineHeight: 1.6 }}>
                    If you have any questions about this project, please contact Jason <NAME_EMAIL>
                  </Typography>
                </Box>
              </CardContent>
            </Card>

            {/* ICU Lipreader Logo - Moved from Header */}
            <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <img
                src="/images/icu-lipreader-logo.png"
                alt="ICU Lipreader Logo"
                style={{
                  width: '270px',
                  height: 'auto',
                  maxHeight: '270px',
                  objectFit: 'contain',
                  borderRadius: '12px',
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
                }}
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
              <Box
                sx={{
                  width: '270px',
                  height: '270px',
                  display: 'none',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: 'rgba(0, 0, 0, 0.1)',
                  borderRadius: 2,
                  border: '2px dashed rgba(0, 0, 0, 0.3)'
                }}
              >
                <Typography variant="body1" sx={{ color: 'rgba(0, 0, 0, 0.6)', textAlign: 'center', fontWeight: 'bold' }}>
                  ICU<br/>Lipreader<br/>Logo
                </Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};

export default ConsentPage;
