import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  CircularProgress,
  Alert,
  Chip,
  Tooltip,
  IconButton,
  Card,
  CardContent,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import RefreshIcon from '@mui/icons-material/Refresh';
import CloudIcon from '@mui/icons-material/Cloud';
import s3ProgressService from '../services/s3ProgressService';

// Static fallback data for production when backend is unavailable
const STATIC_PROGRESS_DATA = {
  overall: {
    progress: 2, // Based on 121 recordings out of 6000 target
    totalRecordings: 121,
    totalTargetRecordings: 6000,
    completedPhrases: 1,
    totalPhrases: 300, // Estimated total phrases for 6000 recordings (20 per phrase)
    phrasesRemaining: 299
  },
  lastUpdated: '2025-07-02T10:00:00.000Z',
  isStatic: true
};

const S3ProgressDisplay = ({
  showDetailedView = false,
  showRefreshButton = false,
  compact = false,
  onProgressUpdate = null
}) => {
  const [progressData, setProgressData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Check if we're in production without backend
  const isProductionWithoutBackend = process.env.NODE_ENV === 'production' &&
    (!process.env.REACT_APP_BACKEND_URL || process.env.REACT_APP_BACKEND_URL.includes('localhost'));

  const fetchProgressData = async (forceRefresh = false) => {
    // Use static data in production when backend is unavailable
    if (isProductionWithoutBackend) {
      console.log('📊 S3ProgressDisplay: Using static progress data for production');
      setProgressData(STATIC_PROGRESS_DATA);
      setLastUpdated(new Date());
      setLoading(false);

      if (onProgressUpdate) {
        onProgressUpdate(STATIC_PROGRESS_DATA);
      }
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log('🔄 S3ProgressDisplay: Fetching progress data, forceRefresh:', forceRefresh);
      const data = await s3ProgressService.fetchProgressData(forceRefresh);
      console.log('✅ S3ProgressDisplay: Progress data received:', {
        overallProgress: data.overall?.progress,
        totalRecordings: data.overall?.totalRecordings,
        completedPhrases: data.overall?.completedPhrases,
        dataSource: data.dataSource
      });

      setProgressData(data);
      setLastUpdated(new Date());

      // Call callback if provided
      if (onProgressUpdate) {
        onProgressUpdate(data);
      }

    } catch (err) {
      console.error('❌ S3ProgressDisplay: Error fetching progress data, falling back to static data:', err);

      // Fallback to static data on error
      setProgressData(STATIC_PROGRESS_DATA);
      setLastUpdated(new Date());
      setError(null); // Clear error to prevent error display

      if (onProgressUpdate) {
        onProgressUpdate(STATIC_PROGRESS_DATA);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProgressData();
  }, [isProductionWithoutBackend]);

  const handleRefresh = () => {
    // In production without backend, just update timestamp to show "refresh" happened
    if (isProductionWithoutBackend) {
      setLastUpdated(new Date());
      console.log('📊 S3ProgressDisplay: Refresh clicked - updating timestamp (static mode)');
      return;
    }
    fetchProgressData(true);
  };

  const formatLastUpdated = (date) => {
    if (!date) return 'Unknown';
    return date.toLocaleString();
  };

  const getProgressColor = (progress) => {
    if (progress >= 80) return '#4caf50'; // Green
    if (progress >= 50) return '#ff9800'; // Orange
    return '#f44336'; // Red
  };

  // Show minimal loading only in development
  if (loading && !isProductionWithoutBackend) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
        <CircularProgress size={24} />
        <Typography variant="body2" color="text.secondary">
          Loading real-time progress from S3...
        </Typography>
      </Box>
    );
  }

  // In production, never show error states - always fall back to static data
  if (!progressData) {
    // This should not happen due to our fallback logic, but just in case
    const fallbackData = STATIC_PROGRESS_DATA;
    return renderProgressDisplay(fallbackData);
  }

  function renderProgressDisplay(data) {
    const { overall } = data;

    if (compact) {
      return (
        <Box sx={{ p: 2, backgroundColor: '#b2dfdb', borderRadius: 2, boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.08)' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: '#00897b' }}>
              Project Progress
            </Typography>
            {showRefreshButton && (
              <Tooltip title={data.isStatic ? "Update timestamp" : "Refresh from S3"}>
                <IconButton size="small" onClick={handleRefresh} disabled={loading}>
                  <RefreshIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="#546e7a">
              Progress toward our goal
            </Typography>
            <Typography variant="body2" color="#00796b" fontWeight="bold">
              {overall.progress}%
            </Typography>
          </Box>

          <Box sx={{ width: '100%', bgcolor: '#e0f2f1', borderRadius: 5, height: 20, overflow: 'hidden' }}>
            <Box
              sx={{
                width: `${overall.progress}%`,
                bgcolor: getProgressColor(overall.progress),
                height: '100%',
                borderRadius: 5,
                transition: 'width 1s ease-in-out'
              }}
            />
          </Box>

          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            {overall.totalRecordings} of {overall.totalTargetRecordings} recordings collected
            {/* Remove stale/error indicators in production */}
            {!isProductionWithoutBackend && progressData.isStale && ' (cached data)'}
          </Typography>

          {/* Only show error alerts in development */}
          {!isProductionWithoutBackend && progressData.error && (
            <Alert severity="warning" sx={{ mt: 1 }}>
              <Typography variant="caption">
                Using cached data: {progressData.error}
              </Typography>
            </Alert>
          )}
        </Box>
      );
    }

    return (
      <Box>
        {/* Overall Progress Card */}
        <Card elevation={3} sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TrendingUpIcon color="primary" />
                Overall Project Progress
              </Typography>
              {showRefreshButton && (
                <Tooltip title={data.isStatic ? "Update timestamp" : "Refresh from S3"}>
                  <IconButton onClick={handleRefresh} disabled={loading}>
                    <RefreshIcon />
                  </IconButton>
                </Tooltip>
              )}
            </Box>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Collection Progress</Typography>
                  <Typography variant="h6" color="primary" fontWeight="bold">
                    {overall.progress}%
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={overall.progress} 
                  sx={{ 
                    height: 12, 
                    borderRadius: 6,
                    backgroundColor: '#e0f2f1',
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: getProgressColor(overall.progress)
                    }
                  }} 
                />
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  {overall.totalRecordings} of {overall.totalTargetRecordings} recordings collected
                </Typography>
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center', py: 2 }}>
                      <Typography variant="h4" color="primary">
                        {overall.completedPhrases}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Phrases Complete
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={6}>
                  <Card variant="outlined">
                    <CardContent sx={{ textAlign: 'center', py: 2 }}>
                      <Typography variant="h4" color="primary">
                        {overall.totalPhrases}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Phrases
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Grid>
          </Grid>

          {/* Data Source and Status */}
          <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
            <Chip 
              icon={<CloudIcon />} 
              label={`Source: ${progressData.dataSource}`} 
              size="small" 
              variant="outlined" 
            />
            <Chip 
              label={`Updated: ${formatLastUpdated(lastUpdated)}`} 
              size="small" 
              variant="outlined" 
            />
            {progressData.isStale && (
              <Chip 
                label="Cached Data" 
                size="small" 
                color="warning" 
              />
            )}
          </Box>

          {progressData.error && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              <Typography variant="body2">
                Using cached data due to error: {progressData.error}
              </Typography>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Detailed Phrase Progress */}
      {showDetailedView && (
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Phrase-by-Phrase Progress</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              {Object.entries(phrases).map(([phrase, data]) => (
                <Grid item xs={12} sm={6} md={4} key={phrase}>
                  <Card variant="outlined" sx={{ height: '100%' }}>
                    <CardContent>
                      <Typography variant="subtitle2" gutterBottom noWrap>
                        {phrase}
                      </Typography>
                      
                      <Box sx={{ mb: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                          <Typography variant="body2">Progress</Typography>
                          <Typography variant="body2" fontWeight="bold">
                            {data.progress}%
                          </Typography>
                        </Box>
                        <LinearProgress 
                          variant="determinate" 
                          value={data.progress} 
                          sx={{ 
                            height: 8, 
                            borderRadius: 4,
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: getProgressColor(data.progress)
                            }
                          }} 
                        />
                      </Box>
                      
                      <Typography variant="body2" color="text.secondary">
                        {data.current} / {data.target} recordings
                      </Typography>
                      
                      {data.completed && (
                        <Chip 
                          label="Complete" 
                          size="small" 
                          color="success" 
                          sx={{ mt: 1 }} 
                        />
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </AccordionDetails>
        </Accordion>
      )}
    </Box>
  );
  }

  // Use the render function with current progress data
  return renderProgressDisplay(progressData);
};

export default S3ProgressDisplay;
