import React from 'react';
import { Box, Button, Menu, MenuItem, IconButton, Typography } from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import HomeIcon from '@mui/icons-material/Home';
import PersonIcon from '@mui/icons-material/Person';
import ListAltIcon from '@mui/icons-material/ListAlt';
import VideocamIcon from '@mui/icons-material/Videocam';
import BarChartIcon from '@mui/icons-material/BarChart';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import BugReportIcon from '@mui/icons-material/BugReport';

const NavigationMenu = ({
  onNavigate,
  currentStep,
  hasConsent,
  demographicsCompleted,
  phrasesSelected
}) => {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleNavigate = (step) => {
    handleClose();
    console.log('NavigationMenu: Navigating to', step);
    onNavigate(step);
  };

  // Define navigation items and their conditions
  const navItems = [
    {
      id: 'demographics',
      label: 'Demographics',
      icon: <PersonIcon fontSize="small" />,
      enabled: true // Always enable demographics if user has reached this point
    },
    {
      id: 'training',
      label: 'Training Video',
      icon: <PlayCircleOutlineIcon fontSize="small" />,
      enabled: hasConsent && demographicsCompleted
    },
    {
      id: 'phrases',
      label: 'Select Phrases',
      icon: <ListAltIcon fontSize="small" />,
      enabled: hasConsent && demographicsCompleted
    },
    {
      id: 'recording',
      label: 'Recording',
      icon: <VideocamIcon fontSize="small" />,
      enabled: hasConsent && demographicsCompleted && phrasesSelected
    },
    {
      id: 'progress',
      label: 'Progress',
      icon: <BarChartIcon fontSize="small" />,
      enabled: hasConsent && demographicsCompleted && phrasesSelected
    },
    {
      id: 'auto-advance-test',
      label: 'Auto-Advance Test',
      icon: <BugReportIcon fontSize="small" />,
      enabled: hasConsent && demographicsCompleted && phrasesSelected
    }
  ];

  return (
    <Box sx={{ position: 'absolute', top: 10, left: 10, zIndex: 1000 }}>
      {/* Consent Status Indicator */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 0.5,
            px: 1.5,
            py: 0.5,
            bgcolor: '#e3f2fd',
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'success.main',
            boxShadow: 1
          }}
        >
          <Typography variant="body2" sx={{ fontWeight: 'medium', color: 'success.dark' }}>
            Consent
          </Typography>
          <Typography variant="body2" sx={{ color: 'success.dark' }}>
            ✅
          </Typography>
        </Box>
      </Box>

      <Button
        id="navigation-button"
        aria-controls={open ? 'navigation-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
        variant="contained"
        color="primary"
        startIcon={<MenuIcon />}
        sx={{
          fontWeight: 'bold',
          boxShadow: 3,
          '&:hover': { bgcolor: 'primary.dark' }
        }}
      >
        Menu
      </Button>
      <Menu
        id="navigation-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'navigation-button',
        }}
      >
        {navItems.map((item) => (
            <MenuItem
              key={item.id}
              onClick={() => item.enabled ? handleNavigate(item.id) : null}
              disabled={!item.enabled}
              selected={currentStep === item.id}
              sx={{
                minWidth: '180px',
                display: 'flex',
                gap: 1,
                opacity: item.enabled ? 1 : 0.5
              }}
            >
              {item.icon}
              {item.label}
            </MenuItem>
          ))}
      </Menu>
    </Box>
  );
};

export default NavigationMenu;
